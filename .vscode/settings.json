{"editor.tabSize": 2, "editor.formatOnSave": true, "[dotenv]": {"editor.defaultFormatter": "foxundermoon.shell-format"}, "[ignore]": {"editor.defaultFormatter": "foxundermoon.shell-format"}, "editor.defaultFormatter": "esbenp.prettier-vscode", "[typescript]": {"editor.defaultFormatter": "biomejs.biome"}, "[javascript]": {"editor.defaultFormatter": "biomejs.biome"}, "[javascriptreact]": {"editor.defaultFormatter": "biomejs.biome"}, "[typescriptreact]": {"editor.defaultFormatter": "biomejs.biome"}, "[json]": {"editor.defaultFormatter": "biomejs.biome"}, "[jsonc]": {"editor.defaultFormatter": "biomejs.biome"}, "[css]": {"editor.defaultFormatter": "biomejs.biome"}, "[graphql]": {"editor.defaultFormatter": "biomejs.biome"}, "typescript.tsdk": "node_modules/typescript/lib", "editor.formatOnPaste": true, "emmet.showExpandedAbbreviation": "never", "editor.codeActionsOnSave": {"source.fixAll.biome": "explicit", "source.organizeImports.biome": "explicit"}}