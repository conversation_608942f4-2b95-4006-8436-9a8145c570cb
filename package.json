{"name": "ai-agent-bus", "version": "1.0.0", "description": "", "main": "", "keywords": [], "author": "", "license": "MIT", "packageManager": "pnpm@10.12.3", "engines": {"node": "^20.9.0"}, "scripts": {"cmt": "git-cz", "dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "pnpm dlx ultracite lint", "format": "pnpm dlx ultracite format", "test": "echo \"Error: no test specified\" && exit 1", "db:push": "dotenv -c pnpm drizzle-kit push", "db:generate": "dotenv -c pnpm drizzle-kit generate", "db:migrate": "dotenv -c pnpm drizzle-kit migrate"}, "dependencies": {"@hookform/resolvers": "^5.1.1", "@node-rs/argon2": "^2.0.2", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-slot": "^1.2.3", "@t3-oss/env-nextjs": "^0.13.8", "@tanstack/react-query": "^5.81.5", "@tanstack/react-query-devtools": "^5.81.5", "@tanstack/react-store": "^0.7.3", "@trpc/client": "^11.4.3", "@trpc/server": "^11.4.3", "@trpc/tanstack-react-query": "^11.4.3", "better-auth": "^1.2.12", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "drizzle-orm": "^0.44.2", "lucide-react": "^0.523.0", "next": "^15.3.4", "next-themes": "^0.4.6", "pino": "^9.7.0", "postgres": "^3.4.7", "react": "^19.1.0", "react-dom": "^19.1.0", "react-error-boundary": "^6.0.0", "react-hook-form": "^7.59.0", "server-only": "^0.0.1", "sonner": "^2.0.6", "superjson": "^2.2.2", "tailwind-merge": "^3.3.1", "tw-animate-css": "^1.3.4", "zod": "^4.0.5"}, "devDependencies": {"@biomejs/biome": "2.1.3", "@commitlint/cli": "^19.8.1", "@commitlint/config-conventional": "^19.8.1", "@commitlint/cz-commitlint": "^19.8.1", "@tailwindcss/postcss": "^4.1.10", "@total-typescript/ts-reset": "^0.6.1", "@types/node": "20.9.0", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "autoprefixer": "^10.4.21", "commitizen": "^4.3.1", "cssnano": "^7.0.7", "dotenv-cli": "^8.0.0", "drizzle-kit": "^0.31.4", "inquirer": "^9.3.7", "lefthook": "^1.12.2", "pino-pretty": "^13.0.0", "postcss": "^8.5.6", "tailwindcss": "^4.1.10", "typescript": "^5.8.3", "ultracite": "5.0.48"}, "config": {"commitizen": {"path": "@commitlint/cz-commitlint"}}}