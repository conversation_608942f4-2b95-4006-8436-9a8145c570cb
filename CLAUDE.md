# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Development Commands

- **Development server**: `pnpm run dev` (starts Next.js with Turbopack)
- **Build**: `pnpm run build` (production build)
- **Production server**: `pnpm run start`
- **Linting**: `pnpm run lint` (uses ultracite)
- **Formatting**: `pnpm run format` (uses ultracite)
- **Commit**: `pnpm run cmt` (guided conventional commits with commitizen)

## Database Commands

- **Push schema**: `pnpm run db:push` (sync schema to database)
- **Generate migrations**: `pnpm run db:generate` (create migration files)
- **Apply migrations**: `pnpm run db:migrate` (run pending migrations)

## Architecture Overview

This is an AI agent proxy/relay service that sits between clients and Claude's API, providing API key management, account management, and usage statistics tracking.

### Core Components

**Claude Relay Service** (`src/services/claude/claudeRelayService.ts`)
- Central proxy service that validates API keys and relays requests to Claude <PERSON>
- Handles model restrictions per API key
- Manages account selection and access token validation
- Tracks usage statistics for billing/monitoring

**Database Schema** (`src/services/db/schema/`)
- `apiKey`: User API keys with model restrictions and metadata
- `claudeAccount`: Claude account credentials and status
- `usage`: Token usage tracking with costs and request metadata
- All use Drizzle ORM with PostgreSQL

**API Architecture**
- tRPC for type-safe API routes (`src/trpc/`)
- Next.js App Router with API routes (`src/app/api/`)
- better-auth for authentication (`src/services/better-auth/`)

### Tech Stack Specifics

- **Next.js 15** with App Router and Turbopack for development
- **tRPC** for end-to-end type safety
- **Drizzle ORM** with PostgreSQL for data persistence
- **better-auth** for authentication
- **Ultracite** (Biome-based) for linting/formatting with strict rules
- **Conventional commits** with commitizen and commitlint

### Environment Setup

Required environment variables (see `.env.example`):
```
NEXT_PUBLIC_DOMAIN=localhost:3000
DATABASE_URL=postgresql://user:password@host:port/database
LOG_LEVEL=info
```

### Code Quality Rules

This project uses Ultracite with comprehensive linting rules from `AGENTS.md`. Key enforcement areas:
- Strict TypeScript usage (no `any`, explicit types)
- React/Next.js best practices (no `<img>`, use Next.js Image)
- Accessibility compliance (ARIA, semantic HTML)
- Security practices (no hardcoded secrets)
- Performance optimizations (prefer `for-of` over `forEach`)

### Key Patterns

- All services are singletons exported as instances
- Database operations use Drizzle ORM with schema validation
- Logging uses Pino logger with structured data
- API key validation includes model restriction checks
- Usage tracking captures costs and token counts per request

### Development Notes

- Use `pnpm` as package manager (configured in package.json)
- Database changes require running `db:generate` then `db:push`
- All commits must follow conventional commit format
- Code must pass ultracite linting before commits (enforced by lefthook)

## Task Master AI Instructions
**Import Task Master's development workflow commands and guidelines, treat as if import is in the main CLAUDE.md file.**
@./.taskmaster/CLAUDE.md
