# AI Agent Bus Project

This document provides a comprehensive overview of the `ai-agent-bus` project, a platform to provide ai agent proxy and token usage statistics.

## Core Purpose

`ai-agent-bus` Acts as a proxy/relay between clients and Claude's API, providing API key management, account management, and usage statistics.

## Tech Stack

- Frontend/Backend: Next.js 15 with App Router
- API Layer: tRPC for type-safe APIs
- Database: PostgreSQL with Drizzle ORM
- Authentication: better-auth
- UI: Shadcn UI + Tailwind CSS
- Development: Biome for linting/formatting

## Getting Started

### Prerequisites

Before you begin, ensure you have the following installed:

- **Node.js**: Version `^20.9.0` or higher.
- **pnpm**: This project uses `pnpm` for package management.
- **PostgreSQL**: A running PostgreSQL database instance.

### Installation

1.  Clone the repository.
2.  Navigate to the project root.
3.  Install the project dependencies:
    ```bash
    pnpm install
    ```

### Environment Variables

Create a `.env` file in the project root based on `.env.example`. The essential variables are:

```
NEXT_PUBLIC_DOMAIN=localhost:3000
DATABASE_URL="postgresql://user:password@host:port/database"
```

### Database Setup

This project uses Drizzle ORM for database schema management.

- **Push schema to database**:
  ```bash
  pnpm run db:push
  ```
- **Generate Drizzle migrations**:
  ```bash
  pnpm run db:generate
  ```
- **Apply database migrations**:
  ```bash
  pnpm run db:migrate
  ```

### Running the Development Server

To start the Next.js development server with Turbopack:

```bash
pnpm run dev
```

The application will be accessible at `http://localhost:3000`.

### Building for Production

To build the project for production:

```bash
pnpm run build
```

To start the production server:

```bash
pnpm run start
```

## Project Structure (`src/` directory)

```mermaid
graph TD
    A[src/] --> B[app/]
    A --> C[components/]
    A --> E[lib/]
    A --> F[services/]
    A --> G[styles/]
    A --> H[trpc/]

    B --> B1[app/api/]
    B --> B2[app/(auth)/]
    B --> B3[app/layout.tsx]
    B --> B4[app/page.tsx]

    B1 --> B1_1[app/api/auth/[...all]/route.ts]
    B1 --> B1_2[app/api/trpc/[trpc]/route.ts]

    C --> C1[components/ui/]

    E --> E1[lib/hooks/]
    E --> E2[lib/server/]
    E --> E3[lib/validations/]

    F --> F1[services/db/]
    F --> F2[services/claude/]
    F --> F3[services/better-auth/]

    F1 --> F1_1[services/db/index.ts]
    F1 --> F1_2[services/db/schema/]

    H --> H1[trpc/client.tsx]
    H --> H2[trpc/index.ts]
    H --> H3[trpc/query-client.ts]
    H --> H4[trpc/server.ts]
    H --> H5[trpc/routers/]
```

- **`src/app/`**: Contains Next.js App Router pages and API routes.
  - `src/app/api/`: Houses Next.js API routes, including the tRPC endpoint and `better-auth` authentication routes.
  - `src/app/(auth)/`: Contains the login and registration pages.
- **`src/components/`**: Stores reusable React components. The `ui/` subdirectory contains UI components from Shadcn UI.
- **`src/lib/`**: A collection of general utility functions, hooks, and validation schemas.
- **`src/services/`**: Contains the core business logic, including database services, Claude API interaction, and authentication.
- **`src/styles/`**: Holds global CSS styles, primarily configured for Tailwind CSS.
- **`src/trpc/`**: Contains the tRPC client and server setup, including router definitions.

## Project Conventions

### Code Formatting and Linting

This project uses Biome to enforce consistent code formatting and linting.

- To format and lint all relevant files:
  ```bash
  pnpm run format
  ```

### Commit Messages

Conventional Commits are enforced using Commitizen and Commitlint to ensure clear and consistent commit history.

- To create a guided commit message:
  ```bash
  pnpm run cmt
  ```
