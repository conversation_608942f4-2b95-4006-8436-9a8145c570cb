import { type ClassValue, clsx } from 'clsx';
import { twMerge } from 'tailwind-merge';

// this is for shadcn/ui
export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

/**
 * Pauses the execution for a specified amount of time.
 * @param time The duration in milliseconds to sleep.
 */
export function sleep(time: number) {
  return new Promise((resolve) => setTimeout(resolve, time));
}
