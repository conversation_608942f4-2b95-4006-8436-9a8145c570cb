import pino from 'pino';
import { serverEnv } from '@/env/server';

const pinoConfig = {
  level: serverEnv.LOG_LEVEL || 'info',
  ...(serverEnv.NODE_ENV === 'development' && {
    transport: {
      target: 'pino-pretty',
      options: {
        colorize: true,
      },
    },
  }),
};

const baseLogger = pino(pinoConfig);

type LogMetadata = Record<string, unknown>;

const logger = {
  fatal(message: string, metadata?: LogMetadata): void {
    baseLogger.fatal({ ...metadata }, message);
  },
  error(message: string, metadata?: LogMetadata): void {
    baseLogger.error({ ...metadata }, message);
  },
  warn(message: string, metadata?: LogMetadata): void {
    baseLogger.warn({ ...metadata }, message);
  },
  info(message: string, metadata?: LogMetadata): void {
    baseLogger.info({ ...metadata }, message);
  },
  debug(message: string, metadata?: LogMetadata): void {
    baseLogger.debug({ ...metadata }, message);
  },
  trace(message: string, metadata?: LogMetadata): void {
    baseLogger.trace({ ...metadata }, message);
  },
  silent(message: string, metadata?: LogMetadata): void {
    baseLogger.silent({ ...metadata }, message);
  },
  success(message: string, metadata: LogMetadata = {}): void {
    this.info(message, { type: 'success', ...metadata });
  },
  start(message: string, metadata: LogMetadata = {}): void {
    this.info(message, { type: 'startup', ...metadata });
  },
  request(
    method: string,
    url: string,
    status: number,
    duration: number,
    metadata: LogMetadata = {}
  ): void {
    const level = status >= 500 ? 'error' : status >= 400 ? 'warn' : 'info';
    this[level](`Request: ${method} ${url} - ${status} (${duration}ms)`, {
      type: 'request',
      method,
      url,
      status,
      duration,
      ...metadata,
    });
  },
  security(message: string, metadata: LogMetadata = {}): void {
    this.warn(`Security: ${message}`, { type: 'security', ...metadata });
  },
  database(message: string, metadata: LogMetadata = {}): void {
    this.debug(`Database: ${message}`, { type: 'database', ...metadata });
  },
  performance(message: string, metadata: LogMetadata = {}): void {
    this.info(`Performance: ${message}`, { type: 'performance', ...metadata });
  },
  audit(message: string, metadata: LogMetadata = {}): void {
    this.info(`Audit: ${message}`, { type: 'audit', ...metadata });
  },
  timer(label: string) {
    const start = Date.now();
    return {
      end: (message = '', metadata: LogMetadata = {}) => {
        const duration = Date.now() - start;
        this.performance(`${label} ${message}`, { duration, ...metadata });
        return duration;
      },
    };
  },
};

logger.start('Logger initialized', {
  level: pinoConfig.level,
  env: serverEnv.NODE_ENV,
});

export default logger;
