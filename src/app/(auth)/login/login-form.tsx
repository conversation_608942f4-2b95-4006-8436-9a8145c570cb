'use client';

import { zod<PERSON>esolver } from '@hookform/resolvers/zod';
import { useMutation } from '@tanstack/react-query';
import { LoaderCircle } from 'lucide-react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { useForm } from 'react-hook-form';
import type * as z from 'zod';
import { Button } from '@/components/ui/button';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { useToast } from '@/lib/hooks/toaster';
import { cn } from '@/lib/utils';
import { User } from '@/lib/validations/auth';
import { authClient } from '@/services/better-auth/auth-client';

const formSchema = User.pick({
  email: true,
  password: true,
});

export function LoginForm() {
  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      email: '',
      password: '',
    },
  });
  const toast = useToast();
  const router = useRouter();

  const signInMutation = useMutation({
    mutationFn: (values: z.infer<typeof formSchema>) => {
      return authClient.signIn.email({
        email: values.email,
        password: values.password,
      });
    },
    onSuccess: (data) => {
      if (data.error) {
        throw new Error(data.error.message);
      }

      toast.success('🎉 登录成功！');
      router.push('/');
    },
  });

  async function onSubmit(values: z.infer<typeof formSchema>) {
    await signInMutation.mutate(values);
  }

  return (
    <Form {...form}>
      <form
        className="flex flex-col gap-6"
        onSubmit={form.handleSubmit(onSubmit)}
      >
        <div className="flex flex-col items-center gap-2 text-center">
          <h1 className="font-bold text-2xl">登录</h1>
          <p className="text-balance text-muted-foreground text-sm">
            🎉 登录到你的账户，开始探索我们的平台吧！
          </p>
        </div>
        <div className="grid gap-6">
          <FormField
            control={form.control}
            name="email"
            render={({ field }) => (
              <FormItem className="grid gap-3">
                <FormLabel>邮箱</FormLabel>
                <FormControl>
                  <Input placeholder="<EMAIL>" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="password"
            render={({ field }) => (
              <FormItem>
                <FormLabel>密码</FormLabel>
                <FormControl>
                  <Input placeholder="******" type="password" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          <Button
            className="w-full"
            disabled={signInMutation.isPending}
            type="submit"
          >
            <LoaderCircle
              className={cn([
                'mr-2 size-4 animate-spin',
                signInMutation.isPending ? 'inline' : 'hidden',
              ])}
            />
            登录
          </Button>
          <FormMessage>{signInMutation.error?.message}</FormMessage>
        </div>
        <div className="text-center text-sm">
          没有账号？
          <Link className="text-blue-500 hover:underline" href="/register">
            点击这里注册
          </Link>
        </div>
      </form>
    </Form>
  );
}
