import { Inter, JetBrains_Mono } from 'next/font/google';
import { Toaster } from '@/components/toaster';
import { TRPCReactProvider } from '@/trpc/client';

import '@/styles/global.css';

const inter = Inter({
  variable: '--font-sans',
  subsets: ['latin'],
});

const jb = JetBrains_Mono({
  subsets: ['latin'],
  variable: '--font-mono',
});

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html dir="ltr" lang="zh-CN" suppressHydrationWarning>
      <body className={`${inter.variable} ${jb.variable}`}>
        <TRPCReactProvider>{children}</TRPCReactProvider>
        <Toaster />
      </body>
    </html>
  );
}
