import { dehydrate, HydrationBoundary } from '@tanstack/react-query';
import {
  createTRPCOptionsProxy,
  type TRPCQueryOptions,
} from '@trpc/tanstack-react-query';
import { cache } from 'react';
import { createCallerFactory, createTRPCContext } from '@/trpc/index';
import { makeQueryClient } from '@/trpc/query-client';
import { appRouter } from '@/trpc/routers/_app';

/**
 * A server-side caller that can be used to call tRPC procedures.
 *
 * If you need access to the data in a server component, we recommend creating a server caller and using it directly. Please note that this method is detached from your query client and does not store the data in the cache. This means that you cannot use the data in a server component and expect it to be available in the client. This is intentional and explained in more detail in the Advanced Server Rendering guide.
 *
 * @example
 * // In your server component
 * import { serverCaller } from "@/trpc/server";
 *
 * export default async function MyPage() {
 *   const data = await serverCaller.hello.sayHello({ name: "World" });
 *
 *   return (
 *     <div>{data.greeting}</div>
 *   );
 * }
 */
const createCaller = createCallerFactory(appRouter);
export const serverCaller = createCaller(createTRPCContext);

/**
 * IMPORTANT: Create a stable getter for the query client that will return the same client during the same request.
 *
 * If you really need to use the data both on the server as well as inside client components and understand the tradeoffs explained in the Advanced Server Rendering guide, you can use fetchQuery instead of prefetch to have the data both on the server as well as hydrating it down to the client.
 */
export const getQueryClient = cache(makeQueryClient);
export const trpc = createTRPCOptionsProxy({
  ctx: createTRPCContext,
  router: appRouter,
  queryClient: getQueryClient,
});

/**
 * A component that dehydrates the query client and passes it to the client side.
 * This is used to pre-fetch data on the server and pass it to the client, so the client doesn't have to refetch the same data.
 *
 * @example
 * // In your server component
 * import { HydrateClient, prefetch } from "@/trpc/server";
 *
 * export default async function MyPage() {
 *   // pre-fetch data on the server
 *   prefetch(trpc.hello.sayHello.queryOptions());
 *
 *   return (
 *     <HydrateClient>
 *       <MyClientComponent />
 *     </HydrateClient>
 *   );
 * }
 */
export function HydrateClient(props: { children: React.ReactNode }) {
  const queryClient = getQueryClient();
  return (
    <HydrationBoundary state={dehydrate(queryClient)}>
      {props.children}
    </HydrationBoundary>
  );
}

/**
 * A helper function to prefetch data on the server, and cache it in the query client.
 * 
 * When you call prefetch({ queryKey: [...] }), you are telling React Query's QueryClient:
"Please fetch the data for this queryKey and store the result in your cache. I don't need the data right now, but a client-side component will soon use useQuery with the same queryKey, and I want it to find the data in the cache immediately instead of making a new network request."
 *
 * @example
 * // In your server component
 * import { prefetch } from "@/trpc/server";
 *
 * export default async function MyPage() {
 *   await prefetch(trpc.hello.sayHello.queryOptions({ name: "World" }));
 *
 *   return (
 *     <div>MyPage</div>
 *   );
 * }
 */
export function prefetch<T extends ReturnType<TRPCQueryOptions<any>>>(
  queryOptions: T
) {
  const queryClient = getQueryClient();
  if (queryOptions.queryKey[1]?.type === 'infinite') {
    void queryClient.prefetchInfiniteQuery(queryOptions as any);
  } else {
    void queryClient.prefetchQuery(queryOptions);
  }
}
