import { z } from 'zod';
import { sleep } from '@/lib/utils';
import { publicProcedure, router } from '@/trpc';

export const helloRouter = router({
  sayHello: publicProcedure
    .input(
      z.object({
        name: z.string().nullish(),
      })
    )
    .query(async ({ input }) => {
      await sleep(3000);
      // throw new Error('fail to load data.');
      return {
        greeting: `Hello ${input.name ?? 'World'} from trpc!`,
      };
    }),
});
