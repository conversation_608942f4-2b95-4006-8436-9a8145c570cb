CREATE TABLE "api_key" (
	"id" varchar(255) PRIMARY KEY NOT NULL,
	"name" varchar(255) NOT NULL,
	"hashed_key" varchar(255) NOT NULL,
	"user_id" varchar(255) NOT NULL,
	"scopes" json,
	"expires_at" timestamp with time zone,
	"created_at" timestamp with time zone DEFAULT now() NOT NULL,
	"updated_at" timestamp with time zone DEFAULT now() NOT NULL,
	"last_used_at" timestamp with time zone,
	"revoked_at" timestamp with time zone,
	"models" json,
	"rate_limit" integer,
	"allowed_ips" json,
	"denied_ips" json,
	"log_level" varchar(255) DEFAULT 'none',
	"status" varchar(255) DEFAULT 'active',
	CONSTRAINT "api_key_hashed_key_unique" UNIQUE("hashed_key")
);
--> statement-breakpoint
CREATE TABLE "claude_account" (
	"id" varchar(255) PRIMARY KEY NOT NULL,
	"name" varchar(255) NOT NULL,
	"email" varchar(255) NOT NULL,
	"access_token" text NOT NULL,
	"refresh_token" text,
	"token_type" varchar(255),
	"expires_in" integer,
	"scope" json,
	"status" varchar(255) DEFAULT 'active',
	"last_used_at" timestamp with time zone,
	"created_at" timestamp with time zone DEFAULT now() NOT NULL,
	"updated_at" timestamp with time zone DEFAULT now() NOT NULL,
	CONSTRAINT "claude_account_email_unique" UNIQUE("email")
);
--> statement-breakpoint
CREATE TABLE "usage" (
	"id" varchar(255) PRIMARY KEY NOT NULL,
	"api_key_id" varchar(255) NOT NULL,
	"claude_account_id" varchar(255) NOT NULL,
	"model" varchar(255) NOT NULL,
	"prompt_tokens" integer NOT NULL,
	"completion_tokens" integer NOT NULL,
	"total_tokens" integer NOT NULL,
	"cost" numeric(10, 5) NOT NULL,
	"ip_address" varchar(255),
	"endpoint" varchar(255),
	"created_at" timestamp with time zone DEFAULT now() NOT NULL
);
--> statement-breakpoint
ALTER TABLE "api_key" ADD CONSTRAINT "api_key_user_id_user_id_fk" FOREIGN KEY ("user_id") REFERENCES "public"."user"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "usage" ADD CONSTRAINT "usage_api_key_id_api_key_id_fk" FOREIGN KEY ("api_key_id") REFERENCES "public"."api_key"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "usage" ADD CONSTRAINT "usage_claude_account_id_claude_account_id_fk" FOREIGN KEY ("claude_account_id") REFERENCES "public"."claude_account"("id") ON DELETE no action ON UPDATE no action;