import { sql } from 'drizzle-orm';
import {
  boolean,
  integer,
  json,
  pgTable,
  text,
  timestamp,
  varchar,
} from 'drizzle-orm/pg-core';

import { user } from './auth';

export const apiKey = pgTable('api_key', {
  id: varchar('id', { length: 255 })
    .primaryKey()
    .$defaultFn(() => crypto.randomUUID()),
  name: varchar('name', { length: 255 }).notNull(),
  hashedKey: varchar('hashed_key', { length: 255 }).notNull().unique(),
  userId: varchar('user_id', { length: 255 })
    .notNull()
    .references(() => user.id),
  scopes: json('scopes').$type<string[]>(),
  expiresAt: timestamp('expires_at', { withTimezone: true }),
  createdAt: timestamp('created_at', { withTimezone: true })
    .notNull()
    .default(sql`now()`),
  updatedAt: timestamp('updated_at', { withTimezone: true })
    .notNull()
    .default(sql`now()`),
  lastUsedAt: timestamp('last_used_at', { withTimezone: true }),
  revokedAt: timestamp('revoked_at', { withTimezone: true }),
  models: json('models').$type<string[]>(),
  rateLimit: integer('rate_limit'),
  allowedIPs: json('allowed_ips').$type<string[]>(),
  deniedIPs: json('denied_ips').$type<string[]>(),
  logLevel: varchar('log_level', { length: 255 }).default('none'),
  status: varchar('status', { length: 255 }).default('active'),
});
