import { sql } from 'drizzle-orm';
import {
  decimal,
  integer,
  pgTable,
  timestamp,
  varchar,
} from 'drizzle-orm/pg-core';

import { apiKey } from './apiKey';
import { claudeAccount } from './claudeAccount';

export const usage = pgTable('usage', {
  id: varchar('id', { length: 255 })
    .primaryKey()
    .$defaultFn(() => crypto.randomUUID()),
  apiKeyId: varchar('api_key_id', { length: 255 })
    .notNull()
    .references(() => apiKey.id),
  claudeAccountId: varchar('claude_account_id', { length: 255 })
    .notNull()
    .references(() => claudeAccount.id),
  model: varchar('model', { length: 255 }).notNull(),
  promptTokens: integer('prompt_tokens').notNull(),
  completionTokens: integer('completion_tokens').notNull(),
  totalTokens: integer('total_tokens').notNull(),
  cost: decimal('cost', { precision: 10, scale: 5 }).notNull(),
  ipAddress: varchar('ip_address', { length: 255 }),
  endpoint: varchar('endpoint', { length: 255 }),
  createdAt: timestamp('created_at', { withTimezone: true })
    .notNull()
    .default(sql`now()`),
});
