import { serverEnv } from '@/env/server';
import logger from '@/lib/server/logger';
import { apiKeyService } from './apiKeyService'; // Assuming this service exists
import { claudeAccountService } from './claudeAccountService'; // Assuming this service exists

class ClaudeRelayService {
  private claudeApiUrl: string;
  private apiVersion: string;

  constructor() {
    this.claudeApiUrl = 'https://api.anthropic.com/v1/messages';
    this.apiVersion = '2023-06-01';
    logger.info('ClaudeRelayService initialized');
  }

  /**
   * Relays a request to the Claude API.
   * @param apiKey - The API key used for authorization.
   * @param body - The request body to be relayed.
   * @returns The response from the Claude API.
   */
  async relay(apiKey: string, body: Record<string, unknown>) {
    const timer = logger.timer('ClaudeRelayService.relay');
    try {
      logger.info('Initiating Claude relay request', {
        apiKey: apiKey.slice(0, 5),
      });

      const apiKeyData = await apiKeyService.validateApiKey(apiKey);
      if (!apiKeyData) {
        logger.warn('Invalid API key provided', { apiKey: apiKey.slice(0, 5) });
        throw new Error('Invalid API key');
      }

      if (apiKeyData.enableModelRestriction) {
        const requestedModel = body.model as string;
        if (apiKeyData.restrictedModels?.includes(requestedModel)) {
          logger.warn('Model restriction violation', {
            apiKey: apiKeyData.name,
            model: requestedModel,
          });
          throw new Error(`Access to model ${requestedModel} is restricted.`);
        }
      }

      const accountId = await claudeAccountService.selectAccount(apiKeyData.id);
      const accessToken =
        await claudeAccountService.getValidAccessToken(accountId);

      const response = await fetch(this.claudeApiUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${accessToken}`,
          'anthropic-version': this.apiVersion,
        },
        body: JSON.stringify(body),
      });

      if (!response.ok) {
        const errorBody = await response.text();
        logger.error('Claude API request failed', {
          status: response.status,
          errorBody,
        });
        throw new Error(
          `Claude API request failed with status ${response.status}`
        );
      }

      const responseData = await response.json();
      logger.success('Successfully relayed request to Claude API', {
        model: body.model,
      });
      return responseData;
    } catch (error) {
      logger.error('Error in Claude relay service', {
        error: (error as Error).message,
      });
      throw error;
    } finally {
      timer.end();
    }
  }

  /**
   * Performs a health check on the service.
   * @returns An object indicating the health status.
   */
  async healthCheck() {
    try {
      const activeAccounts =
        await claudeAccountService.getActiveAccountsCount();
      const isHealthy = activeAccounts > 0;
      logger.info('Health check performed', { isHealthy, activeAccounts });
      return {
        healthy: isHealthy,
        activeAccounts,
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      logger.error('Health check failed', { error: (error as Error).message });
      return {
        healthy: false,
        error: (error as Error).message,
        timestamp: new Date().toISOString(),
      };
    }
  }
}

export const claudeRelayService = new ClaudeRelayService();
